'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import ClothingDetail from '@/components/clothing/ClothingDetail';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, AlertCircle } from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  brand?: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  originalPrice?: number;
  images: string[];
  views: number;
  likes: string[];
  likeCount: number;
  tags: string[];
  preferredSwapCategories?: string[];
  location: {
    county: string;
    town: string;
  };
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    rating: {
      average: number;
      count: number;
    };
    sustainabilityScore: number;
    location: {
      county: string;
      town: string;
    };
  };
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo?: string;
  };
  qualityAssurance?: {
    isVerified: boolean;
    verificationDate?: string;
    verifiedBy?: string;
    notes?: string;
  };
  createdAt: string;
  updatedAt: string;
  isAvailable: boolean;
}

export default function ItemDetailPage() {
  const [item, setItem] = useState<ClothingItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const router = useRouter();
  const params = useParams();
  const itemId = params.id as string;

  useEffect(() => {
    // Fetch current user and item details
    fetchCurrentUser();
    fetchItem();
  }, [itemId]);

  const fetchCurrentUser = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      setCurrentUserId(null);
      return;
    }

    try {
      const response = await fetch('/api/users/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentUserId(data.data.user.id);
      } else {
        console.error('Failed to fetch user profile');
        setCurrentUserId(null);
      }
    } catch (error) {
      console.error('Error fetching current user:', error);
      setCurrentUserId(null);
    }
  };

  const fetchItem = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/clothing/${itemId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch item');
      }

      setItem(data.data);
    } catch (err) {
      console.error('Error fetching item:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch item');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login');
      return;
    }

    try {
      const response = await fetch(`/api/clothing/${itemId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to like item');
      }

      // Update the item state
      if (item && currentUserId) {
        const isCurrentlyLiked = item.likes.includes(currentUserId);
        setItem(prev => prev ? {
          ...prev,
          likes: isCurrentlyLiked
            ? prev.likes.filter(id => id !== currentUserId)
            : [...prev.likes, currentUserId],
          likeCount: isCurrentlyLiked ? prev.likeCount - 1 : prev.likeCount + 1,
        } : null);
      }
    } catch (error) {
      console.error('Error liking item:', error);
      setError('Failed to like item');
    }
  };

  const handleContact = () => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login');
      return;
    }

    // TODO: Implement chat/messaging functionality
    alert('Contact functionality will be implemented in the next phase!');
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="aspect-square bg-gray-200 rounded-lg" />
                <div className="grid grid-cols-4 gap-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className="aspect-square bg-gray-200 rounded-lg" />
                  ))}
                </div>
              </div>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="h-8 bg-gray-200 rounded w-3/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                  <div className="flex gap-2">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="h-6 bg-gray-200 rounded w-16" />
                    ))}
                  </div>
                </div>
                <div className="space-y-3">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !item) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>

          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Item not found'}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 overflow-y-auto">
      <div className="max-w-6xl mx-auto">
        {/* Back Button */}
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="mb-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        {/* Item Detail */}
        <ClothingDetail
          item={item}
          currentUserId={currentUserId}
          onLike={handleLike}
          onContact={handleContact}
        />
      </div>
    </div>
  );
}
