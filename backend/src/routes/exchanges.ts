import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticate } from '../middleware/auth';
import { AuthenticatedRequest } from '../types/auth';
import { exchangeService } from '../services/exchange';
import { trackUserActivity } from '../middleware/tokenRewards';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.log('=== VALIDATION ERRORS ===');
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('Validation errors:', errors.array());
    res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
    return;
  }
  next();
};

// Create a new exchange offer
router.post('/offers',
  authenticate,
  trackUserActivity('exchange_offer_created'),
  [
    body('type').isIn(['swap', 'token_purchase', 'donation']).withMessage('Invalid exchange type'),
    body('targetUserId').isMongoId().withMessage('Invalid target user ID'),
    body('requestedItems').isArray({ min: 1 }).withMessage('At least one requested item is required'),
    body('requestedItems.*').isMongoId().withMessage('Invalid item ID'),
    body('offeredItems').optional().isArray(),
    body('offeredItems.*').optional().isMongoId().withMessage('Invalid offered item ID'),
    body('tokenAmount').optional().isNumeric().isFloat({ min: 1 }).withMessage('Token amount must be positive'),
    body('message').optional().isLength({ max: 500 }).withMessage('Message too long'),
    body('conditions').optional().isLength({ max: 300 }).withMessage('Conditions too long'),
    body('preferredDeliveryMethod').isIn(['pickup', 'delivery', 'meetup']).withMessage('Invalid delivery method'),
    body('deliveryNotes').optional().isLength({ max: 200 }).withMessage('Delivery notes too long'),
    body('charityPartnerId').optional().isMongoId().withMessage('Invalid charity partner ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      console.log('=== EXCHANGE OFFER REQUEST ===');
      console.log('Request body:', JSON.stringify(req.body, null, 2));
      console.log('User ID:', req.user?.id);

      const userId = req.user!.id;
      const { targetUserId, ...offerData } = req.body;

      if (userId === targetUserId) {
        return res.status(400).json({
          success: false,
          message: 'You cannot create an offer to yourself'
        });
      }

      const offer = await exchangeService.createOffer(userId, targetUserId, offerData);

      res.status(201).json({
        success: true,
        message: 'Exchange offer created successfully',
        data: offer
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to create exchange offer'
      });
    }
  }
);

// Get user's offers (received and sent)
router.get('/offers',
  authenticate,
  [
    query('type').optional().isIn(['received', 'sent']).withMessage('Invalid type filter'),
    query('status').optional().isIn(['pending', 'accepted', 'declined', 'expired', 'withdrawn']).withMessage('Invalid status filter'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { type, status } = req.query;

      const offers = await exchangeService.getUserOffers(
        userId,
        type as 'received' | 'sent',
        status as string
      );

      res.json({
        success: true,
        data: offers
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch offers'
      });
    }
  }
);

// Get specific offer details
router.get('/offers/:offerId',
  authenticate,
  [
    param('offerId').isMongoId().withMessage('Invalid offer ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { offerId } = req.params;

      const offer = await exchangeService.getOfferDetails(offerId, userId);

      res.json({
        success: true,
        data: offer
      });
    } catch (error: any) {
      res.status(404).json({
        success: false,
        message: error.message || 'Offer not found'
      });
    }
  }
);

// Accept an exchange offer
router.post('/offers/:offerId/accept',
  authenticate,
  trackUserActivity('exchange_offer_accepted'),
  [
    param('offerId').isMongoId().withMessage('Invalid offer ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { offerId } = req.params;

      const transaction = await exchangeService.acceptOffer(offerId, userId);

      res.json({
        success: true,
        message: 'Offer accepted successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to accept offer'
      });
    }
  }
);

// Decline an exchange offer
router.post('/offers/:offerId/decline',
  authenticate,
  trackUserActivity('exchange_offer_declined'),
  [
    param('offerId').isMongoId().withMessage('Invalid offer ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { offerId } = req.params;

      const offer = await exchangeService.declineOffer(offerId, userId);

      res.json({
        success: true,
        message: 'Offer declined successfully',
        data: offer
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to decline offer'
      });
    }
  }
);

// Withdraw an exchange offer
router.post('/offers/:offerId/withdraw',
  authenticate,
  trackUserActivity('exchange_offer_withdrawn'),
  [
    param('offerId').isMongoId().withMessage('Invalid offer ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { offerId } = req.params;

      const offer = await exchangeService.withdrawOffer(offerId, userId);

      res.json({
        success: true,
        message: 'Offer withdrawn successfully',
        data: offer
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to withdraw offer'
      });
    }
  }
);

// Create a counter offer
router.post('/offers/:offerId/counter',
  authenticate,
  trackUserActivity('exchange_counter_offer_created'),
  [
    param('offerId').isMongoId().withMessage('Invalid offer ID'),
    body('tokenAmount').optional().isNumeric().isFloat({ min: 1 }).withMessage('Token amount must be positive'),
    body('offeredItems').optional().isArray(),
    body('offeredItems.*').optional().isMongoId().withMessage('Invalid offered item ID'),
    body('message').optional().isLength({ max: 300 }).withMessage('Message too long'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { offerId } = req.params;
      const counterOfferData = req.body;

      const offer = await exchangeService.createCounterOffer(offerId, userId, counterOfferData);

      res.json({
        success: true,
        message: 'Counter offer created successfully',
        data: offer
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to create counter offer'
      });
    }
  }
);

// Get exchange statistics for user
router.get('/stats',
  authenticate,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;

      // This would be implemented with aggregation pipelines
      // For now, returning placeholder data
      const stats = {
        totalOffersSent: 0,
        totalOffersReceived: 0,
        totalAcceptedOffers: 0,
        totalCompletedExchanges: 0,
        averageResponseTime: 0,
        successRate: 0
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch exchange statistics'
      });
    }
  }
);

export default router;
