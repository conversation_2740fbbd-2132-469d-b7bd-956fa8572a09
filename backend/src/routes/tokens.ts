import { Router } from 'express';
import { body, query, validationResult } from 'express-validator';
import { authenticate } from '@/middleware/auth';
import { validatePagination } from '@/middleware/validation';
import { tokenService } from '@/services/token';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

const router = Router();

// Get user's token balance
router.get('/balance', authenticate, async (req, res, next) => {
  try {
    const user = req.user;

    res.json({
      success: true,
      data: {
        balance: user.pediTokens,
        userId: user._id,
        lastUpdated: user.updatedAt
      }
    });

  } catch (error) {
    logger.error('Error getting token balance:', error);
    next(createError('Failed to get token balance', 500));
  }
});

// Get token transaction history
router.get('/history',
  authenticate,
  validatePagination,
  [
    query('type')
      .optional()
      .isIn(['earn', 'spend', 'bonus', 'refund', 'penalty'])
      .withMessage('Invalid transaction type'),
    query('source')
      .optional()
      .isIn(['listing', 'donation', 'swap', 'purchase', 'referral', 'welcome', 'daily_login', 'review', 'admin'])
      .withMessage('Invalid source type'),
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400, errors.array()));
      }

      const user = req.user;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const type = req.query.type as string;
      const source = req.query.source as string;

      const result = await tokenService.getTokenHistory(user._id.toString(), {
        page,
        limit,
        type: type as any,
        source: source as any
      });

      if (!result.success) {
        return next(createError(result.error || 'Failed to get token history', 500));
      }

      res.json({
        success: true,
        data: {
          balance: user.pediTokens,
          transactions: result.transactions,
          pagination: result.pagination
        }
      });

    } catch (error) {
      logger.error('Error getting token history:', error);
      next(createError('Failed to get token history', 500));
    }
  }
);

// Manual token earning (admin only)
router.post('/earn',
  authenticate,
  [
    body('userId')
      .notEmpty()
      .withMessage('User ID is required')
      .isMongoId()
      .withMessage('Invalid user ID'),
    body('amount')
      .isInt({ min: 1 })
      .withMessage('Amount must be a positive integer'),
    body('description')
      .notEmpty()
      .withMessage('Description is required')
      .isLength({ min: 5, max: 200 })
      .withMessage('Description must be between 5 and 200 characters'),
    body('referenceId')
      .optional()
      .isMongoId()
      .withMessage('Invalid reference ID'),
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400, errors.array()));
      }

      const user = req.user;

      // Only admins can manually award tokens
      if (user.role !== 'admin') {
        return next(createError('Unauthorized: Admin access required', 403));
      }

      const { userId, amount, description, referenceId, metadata } = req.body;

      const result = await tokenService.earnTokens({
        userId,
        action: 'admin',
        amount,
        description,
        referenceId,
        metadata: {
          ...metadata,
          awardedBy: user._id,
          awardedByName: `${user.firstName} ${user.lastName}`
        }
      });

      if (!result.success) {
        return next(createError(result.error || 'Failed to award tokens', 500));
      }

      res.json({
        success: true,
        data: {
          tokensAwarded: amount,
          newBalance: result.newBalance,
          description
        }
      });

    } catch (error) {
      logger.error('Error awarding tokens:', error);
      next(createError('Failed to award tokens', 500));
    }
  }
);

// Daily login bonus
router.post('/daily-bonus', authenticate, async (req, res, next): Promise<void> => {
  try {
    const user = req.user;

    const result = await tokenService.processDailyLoginBonus(user._id.toString());

    if (!result.success) {
      res.json({
        success: false,
        message: 'Daily bonus already claimed today',
        data: {
          tokensEarned: 0,
          nextBonusAvailable: new Date(Date.now() + 24 * 60 * 60 * 1000) // Next day
        }
      });
    }

    res.json({
      success: true,
      data: {
        tokensEarned: result.tokensEarned,
        newBalance: result.newBalance || 0,
        message: 'Daily bonus claimed successfully!'
      }
    });

  } catch (error) {
    logger.error('Error processing daily bonus:', error);
    next(createError('Failed to process daily bonus', 500));
  }
});

// Get token leaderboard
router.get('/leaderboard',
  [
    query('limit')
      .optional()
      .isInt({ min: 5, max: 50 })
      .withMessage('Limit must be between 5 and 50'),
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400, errors.array()));
      }

      const limit = parseInt(req.query.limit as string) || 10;

      const leaderboard = await tokenService.getTokenLeaderboard(limit);

      res.json({
        success: true,
        data: {
          leaderboard,
          totalUsers: leaderboard.length
        }
      });

    } catch (error) {
      logger.error('Error getting token leaderboard:', error);
      next(createError('Failed to get token leaderboard', 500));
    }
  }
);

// Get token statistics
router.get('/stats', authenticate, async (req, res, next) => {
  try {
    const user = req.user;

    const stats = await tokenService.getTokenStats(user._id.toString());

    if (!stats.success) {
      return next(createError(stats.error || 'Failed to get token statistics', 500));
    }

    res.json({
      success: true,
      data: stats.data
    });

  } catch (error) {
    logger.error('Error getting token stats:', error);
    next(createError('Failed to get token statistics', 500));
  }
});

// Get earning opportunities
router.get('/opportunities', authenticate, async (req, res, next) => {
  try {
    const user = req.user;

    const opportunities = await tokenService.getEarningOpportunities(user._id.toString());

    res.json({
      success: true,
      data: {
        opportunities,
        currentBalance: user.pediTokens
      }
    });

  } catch (error) {
    logger.error('Error getting earning opportunities:', error);
    next(createError('Failed to get earning opportunities', 500));
  }
});

// Transfer tokens between users (future feature)
router.post('/transfer',
  authenticate,
  [
    body('recipientId')
      .notEmpty()
      .withMessage('Recipient ID is required')
      .isMongoId()
      .withMessage('Invalid recipient ID'),
    body('amount')
      .isInt({ min: 1 })
      .withMessage('Amount must be a positive integer'),
    body('message')
      .optional()
      .isLength({ max: 200 })
      .withMessage('Message must be less than 200 characters'),
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError('Validation failed', 400, errors.array()));
      }

      const user = req.user;
      const { recipientId, amount, message } = req.body;

      // Check if user has enough tokens
      if (user.pediTokens < amount) {
        return next(createError('Insufficient token balance', 400));
      }

      // Prevent self-transfer
      if (user._id.toString() === recipientId) {
        return next(createError('Cannot transfer tokens to yourself', 400));
      }

      const result = await tokenService.transferTokens({
        senderId: user._id.toString(),
        recipientId,
        amount,
        message: message || `Token transfer from ${user.firstName} ${user.lastName}`
      });

      if (!result.success) {
        return next(createError(result.error || 'Failed to transfer tokens', 500));
      }

      res.json({
        success: true,
        data: {
          tokensTransferred: amount,
          newBalance: result.senderNewBalance,
          recipientNewBalance: result.recipientNewBalance,
          message: 'Tokens transferred successfully!'
        }
      });

    } catch (error) {
      logger.error('Error transferring tokens:', error);
      next(createError('Failed to transfer tokens', 500));
    }
  }
);

export default router;
