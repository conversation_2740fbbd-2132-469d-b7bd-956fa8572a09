import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticate } from '@/middleware/auth';
import { handleValidationErrors } from '@/middleware/validation';
import { paymentService } from '@/services/payment';
import { logger } from '@/utils/logger';
import { AuthenticatedRequest } from '@/types/auth';

const router = Router();

// Create a new payment
router.post('/',
  authenticate,
  [
    body('amount').isFloat({ min: 1 }).withMessage('Amount must be greater than 0'),
    body('description').isLength({ min: 1, max: 200 }).withMessage('Description is required and must be less than 200 characters'),
    body('phoneNumber').optional().isMobilePhone('any').withMessage('Invalid phone number'),
    body('paymentMethodId').optional().isMongoId().withMessage('Invalid payment method ID'),
    body('relatedTransaction').optional().isMongoId().withMessage('Invalid transaction ID'),
    body('relatedEntity.type').optional().isIn(['token_purchase', 'platform_fee', 'premium_feature', 'delivery_fee']).withMessage('Invalid entity type'),
    body('relatedEntity.id').optional().isMongoId().withMessage('Invalid entity ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;
      const paymentData = req.body;

      const payment = await paymentService.createPayment({
        userId,
        ...paymentData,
      });

      res.status(201).json({
        success: true,
        message: 'Payment created successfully',
        data: payment,
      });
    } catch (error: any) {
      logger.error('Failed to create payment:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to create payment',
      });
    }
  }
);

// Initiate STK Push payment
router.post('/:paymentId/stk-push',
  authenticate,
  [
    param('paymentId').isLength({ min: 1 }).withMessage('Payment ID is required'),
    body('phoneNumber').isMobilePhone('any').withMessage('Valid phone number is required'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const { paymentId } = req.params;
      const { phoneNumber } = req.body;

      const payment = await paymentService.initiateSTKPush(paymentId, phoneNumber);

      res.json({
        success: true,
        message: 'STK Push initiated successfully',
        data: {
          paymentId: payment.paymentId,
          merchantRequestId: payment.merchantRequestId,
          checkoutRequestId: payment.checkoutRequestId,
          status: payment.status,
        },
      });
    } catch (error: any) {
      logger.error('Failed to initiate STK Push:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to initiate STK Push',
      });
    }
  }
);

// M-Pesa callback endpoint
router.post('/callback/mpesa',
  async (req, res): Promise<void> => {
    try {
      logger.info('M-Pesa callback received:', req.body);

      // Validate callback signature if configured
      const signature = req.headers['x-mpesa-signature'] as string;
      if (signature && process.env['MPESA_CALLBACK_SECRET']) {
        const isValid = require('@/services/mpesa').getMpesaService().validateCallbackSignature(
          JSON.stringify(req.body),
          signature
        );
        
        if (!isValid) {
          logger.warn('Invalid M-Pesa callback signature');
          return res.status(401).json({ success: false, message: 'Invalid signature' });
        }
      }

      await paymentService.processCallback(req.body);

      res.json({
        success: true,
        message: 'Callback processed successfully',
      });
    } catch (error: any) {
      logger.error('Failed to process M-Pesa callback:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process callback',
      });
    }
  }
);

// Query payment status
router.get('/:paymentId/status',
  authenticate,
  [
    param('paymentId').isLength({ min: 1 }).withMessage('Payment ID is required'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const { paymentId } = req.params;

      const payment = await paymentService.queryPaymentStatus(paymentId);

      res.json({
        success: true,
        data: {
          paymentId: payment.paymentId,
          status: payment.status,
          amount: payment.amount,
          description: payment.description,
          mpesaReceiptNumber: payment.mpesaReceiptNumber,
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt,
        },
      });
    } catch (error: any) {
      logger.error('Failed to query payment status:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to query payment status',
      });
    }
  }
);

// Get payment details
router.get('/:paymentId',
  authenticate,
  [
    param('paymentId').isLength({ min: 1 }).withMessage('Payment ID is required'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const { paymentId } = req.params;
      const userId = req.user!.id;

      const payment = await paymentService.getPayment(paymentId);
      
      if (!payment) {
        return res.status(404).json({
          success: false,
          message: 'Payment not found',
        });
      }

      // Check if user owns this payment
      if (payment.user.toString() !== userId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
        });
      }

      res.json({
        success: true,
        data: payment,
      });
    } catch (error: any) {
      logger.error('Failed to get payment:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to get payment',
      });
    }
  }
);

// Get user payments with pagination
router.get('/',
  authenticate,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional().isIn(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded']).withMessage('Invalid status'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const status = req.query.status as string;

      const result = await paymentService.getUserPayments(userId, page, limit, status);

      res.json({
        success: true,
        data: result.payments,
        pagination: {
          page,
          limit,
          total: result.total,
          pages: result.pages,
        },
      });
    } catch (error: any) {
      logger.error('Failed to get user payments:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to get payments',
      });
    }
  }
);

// Update payment status (admin only)
router.patch('/:paymentId/status',
  authenticate,
  [
    param('paymentId').isLength({ min: 1 }).withMessage('Payment ID is required'),
    body('status').isIn(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded']).withMessage('Invalid status'),
    body('note').optional().isLength({ max: 200 }).withMessage('Note must be less than 200 characters'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      // Check if user is admin
      if (req.user!.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Admin privileges required.',
        });
      }

      const { paymentId } = req.params;
      const { status, note } = req.body;

      const payment = await paymentService.updatePaymentStatus({
        paymentId,
        status,
        note,
        source: 'manual',
      });

      res.json({
        success: true,
        message: 'Payment status updated successfully',
        data: payment,
      });
    } catch (error: any) {
      logger.error('Failed to update payment status:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to update payment status',
      });
    }
  }
);

// Get payment statistics (admin only)
router.get('/admin/statistics',
  authenticate,
  async (req: AuthenticatedRequest, res): Promise<void> => {
    try {
      // Check if user is admin
      if (req.user!.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Admin privileges required.',
        });
        return;
      }

      // This would typically use aggregation pipelines
      // For now, return a placeholder response
      res.json({
        success: true,
        message: 'Payment statistics endpoint - Coming soon',
        data: {
          totalPayments: 0,
          totalAmount: 0,
          successRate: 0,
          averageAmount: 0,
        },
      });
    } catch (error: any) {
      logger.error('Failed to get payment statistics:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to get payment statistics',
      });
    }
  }
);

export default router;
