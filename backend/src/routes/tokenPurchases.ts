import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticate } from '../middleware/auth';
import { tokenPurchaseService } from '../services/tokenPurchase';
import { trackUserActivity } from '../middleware/tokenRewards';
import { AuthenticatedRequest } from '../types/auth';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Create a token purchase transaction
router.post('/',
  authenticate,
  trackUserActivity('token_purchase_initiated'),
  [
    body('itemId').isMongoId().withMessage('Invalid item ID'),
    body('offeredAmount').isNumeric().isFloat({ min: 1 }).withMessage('Offered amount must be positive'),
    body('deliveryMethod').isIn(['pickup', 'delivery', 'meetup']).withMessage('Invalid delivery method'),
    body('deliveryAddress').optional().isObject(),
    body('deliveryAddress.county').optional().isString().withMessage('County is required for delivery'),
    body('deliveryAddress.town').optional().isString().withMessage('Town is required for delivery'),
    body('deliveryAddress.specificLocation').optional().isString().withMessage('Specific location is required for delivery'),
    body('deliveryAddress.contactPhone').optional().matches(/^(\+254|0)[17]\d{8}$/).withMessage('Invalid phone number'),
    body('meetupLocation').optional().isObject(),
    body('meetupLocation.name').optional().isString().withMessage('Meetup location name is required'),
    body('meetupLocation.address').optional().isString().withMessage('Meetup address is required'),
    body('meetupLocation.scheduledTime').optional().isISO8601().withMessage('Invalid scheduled time'),
    body('message').optional().isLength({ max: 300 }).withMessage('Message too long'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const purchaseData = req.body;

      const transaction = await tokenPurchaseService.createTokenPurchase(userId, purchaseData);

      res.status(201).json({
        success: true,
        message: 'Token purchase created successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to create token purchase'
      });
    }
  }
);

// Accept a token purchase offer
router.post('/:transactionId/accept',
  authenticate,
  trackUserActivity('token_purchase_accepted'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;

      const transaction = await tokenPurchaseService.acceptTokenPurchase(transactionId, userId);

      res.json({
        success: true,
        message: 'Token purchase accepted successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to accept token purchase'
      });
    }
  }
);

// Decline a token purchase offer
router.post('/:transactionId/decline',
  authenticate,
  trackUserActivity('token_purchase_declined'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('reason').optional().isLength({ max: 300 }).withMessage('Reason too long'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;
      const { reason } = req.body;

      const transaction = await tokenPurchaseService.declineTokenPurchase(transactionId, userId, reason);

      res.json({
        success: true,
        message: 'Token purchase declined successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to decline token purchase'
      });
    }
  }
);

// Create a counter offer
router.post('/:transactionId/counter-offer',
  authenticate,
  trackUserActivity('token_purchase_counter_offered'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('counterOffer').isNumeric().isFloat({ min: 1 }).withMessage('Counter offer must be positive'),
    body('message').optional().isLength({ max: 300 }).withMessage('Message too long'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;
      const negotiationData = req.body;

      const transaction = await tokenPurchaseService.createCounterOffer(transactionId, userId, negotiationData);

      res.json({
        success: true,
        message: 'Counter offer created successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to create counter offer'
      });
    }
  }
);

// Accept a counter offer
router.post('/:transactionId/accept-counter-offer',
  authenticate,
  trackUserActivity('token_purchase_counter_offer_accepted'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;

      const transaction = await tokenPurchaseService.acceptCounterOffer(transactionId, userId);

      res.json({
        success: true,
        message: 'Counter offer accepted successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to accept counter offer'
      });
    }
  }
);

// Complete a token purchase transaction
router.post('/:transactionId/complete',
  authenticate,
  trackUserActivity('token_purchase_completed'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('rating').optional().isFloat({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('comment').optional().isLength({ max: 300 }).withMessage('Comment too long'),
    body('qualityConfirmed').isBoolean().withMessage('Quality confirmation is required'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;
      const completionData = req.body;

      const transaction = await tokenPurchaseService.completeTokenPurchase(transactionId, userId, completionData);

      res.json({
        success: true,
        message: 'Token purchase completed successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to complete token purchase'
      });
    }
  }
);

// Get purchase recommendations
router.get('/recommendations',
  authenticate,
  [
    query('budget').optional().isNumeric().isFloat({ min: 1 }).withMessage('Budget must be positive'),
    query('category').optional().isString().withMessage('Invalid category'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { budget, category } = req.query;

      const recommendations = await tokenPurchaseService.getPurchaseRecommendations(
        userId,
        budget ? parseInt(budget as string) : undefined,
        category as string
      );

      res.json({
        success: true,
        data: recommendations
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch purchase recommendations'
      });
    }
  }
);

// Get purchase history for user
router.get('/history',
  authenticate,
  [
    query('type').optional().isIn(['purchases', 'sales']).withMessage('Invalid type filter'),
    query('status').optional().isIn(['pending', 'accepted', 'completed', 'declined']).withMessage('Invalid status filter'),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { type, status, page = 1, limit = 10 } = req.query;

      // This would be implemented with proper pagination and filtering
      // For now, returning placeholder response
      const purchases = {
        transactions: [],
        pagination: {
          currentPage: parseInt(page as string),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit as string)
        }
      };

      res.json({
        success: true,
        data: purchases
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch purchase history'
      });
    }
  }
);

// Get purchase statistics for user
router.get('/stats',
  authenticate,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;

      // This would be implemented with aggregation pipelines
      // For now, returning placeholder data
      const stats = {
        totalPurchases: 0,
        totalSales: 0,
        totalSpent: 0,
        totalEarned: 0,
        averagePurchasePrice: 0,
        averageSalePrice: 0,
        averageRating: 0,
        monthlyPurchases: [],
        topCategories: []
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch purchase statistics'
      });
    }
  }
);

export default router;
